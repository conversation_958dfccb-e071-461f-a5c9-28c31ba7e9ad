@model CinemaBooking.Models.DatVe

@{
    ViewData["Title"] = "Thanh toán thành công";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">Đặt vé thành công!</h4>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-check-circle text-success fa-5x mb-3"></i>
                        <h5>Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi!</h5>
                        <p class="text-muted">Vé xem phim của bạn đã được xác nhận</p>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">Thông tin vé</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>Mã đặt vé:</strong> #@Model.MaDatVe</p>
                                            <p><strong>Ngày đặt:</strong> @Model.NgayDat?.ToString("dd/MM/yyyy HH:mm")</p>
                                            <p><strong>Phim:</strong> @Model.LichChieu.Phim.TenPhim</p>
                                            <p><strong>Rạp:</strong> @Model.LichChieu.PhongChieu.RapPhim.TenRap</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>Phòng:</strong> @Model.LichChieu.PhongChieu.TenPhong</p>
                                            <p><strong>Suất chiếu:</strong> @Model.LichChieu.NgayChieu.ToString("dd/MM/yyyy") @Model.LichChieu.GioChieu.ToString(@"hh\:mm")</p>
                                            <p><strong>Ghế:</strong> 
                                                @foreach (var datVeGhe in Model.DatVeGhes)
                                                {
                                                    <span class="badge bg-info me-1">@datVeGhe.Ghe.SoGhe</span>
                                                }
                                            </p>
                                            <p><strong>Tổng tiền:</strong> @Model.TongTien.ToString("N0") VNĐ</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if (Model.ThanhToans != null && Model.ThanhToans.Any())
                    {
                        var thanhToan = Model.ThanhToans
                            .Where(t => t.TrangThai == "Thành công")
                            .OrderByDescending(t => t.NgayThanhToan)
                            .FirstOrDefault() ?? Model.ThanhToans.FirstOrDefault();
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">Thông tin thanh toán</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Phương thức:</strong> 
                                                @{
                                                    string paymentMethod = thanhToan.PhuongThucThanhToan;
                                                    
                                                    switch (paymentMethod) {
                                                        case "MOMO":
                                                            <span class="text-primary">MoMo</span>
                                                            break;
                                                        case "VNPAY":
                                                            <span class="text-info">VNPAY</span>
                                                            break;
                                                        case "Tại rạp":
                                                            <span class="text-secondary">Thanh toán tại rạp</span>
                                                            break;
                                                        default:
                                                            <span>@paymentMethod</span>
                                                            break;
                                                    }
                                                }</p>
                                                <p><strong>Trạng thái:</strong> 
                                                @{
                                                    string status = thanhToan.TrangThai;
                                                    
                                                    switch (status) {
                                                        case "Thành công":
                                                            <span class="badge bg-success">Thành công</span>
                                                            break;
                                                        case "Chờ thanh toán":
                                                            <span class="badge bg-warning text-dark">Chờ thanh toán</span>
                                                            break;
                                                        case "Thất bại":
                                                            <span class="badge bg-danger">Thất bại</span>
                                                            break;
                                                        default:
                                                            <span class="badge bg-secondary">@status</span>
                                                            break;
                                                    }
                                                }</p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Mã giao dịch:</strong> @thanhToan.MaGiaoDichNganHang</p>
                                                <p><strong>Thời gian:</strong> @thanhToan.NgayThanhToan?.ToString("dd/MM/yyyy HH:mm")</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <div class="alert alert-info mb-4">
                        <p class="mb-1"><i class="fas fa-info-circle me-2"></i>Vé điện tử đã được gửi đến email của bạn</p>
                        <p class="mb-0"><i class="fas fa-exclamation-circle me-2"></i>Vui lòng xuất trình vé khi đến rạp</p>
                    </div>

                    <div class="text-center">
                        <div class="btn-group mb-3">
                            <a href="@Url.Action("Print", "DatVe", new { maDatVe = Model.MaDatVe })" class="btn btn-outline-primary">
                                <i class="fas fa-print me-1"></i> In vé
                            </a>
                            <button type="button" class="btn btn-outline-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                <span class="visually-hidden">Chọn kiểu in</span>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="@Url.Action("Print", "DatVe", new { maDatVe = Model.MaDatVe })">
                                        <i class="fas fa-ticket-alt me-1"></i> In vé tổng hợp
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="@Url.Action("PrintMultiple", "DatVe", new { maDatVe = Model.MaDatVe })">
                                        <i class="fas fa-th-list me-1"></i> In vé theo từng ghế
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class="mt-2">
                            <a href="@Url.Action("LichSuDatVe", "Home")" class="btn btn-primary me-2">
                                <i class="fas fa-history me-1"></i>Lịch sử đặt vé
                            </a>
                            <a href="@Url.Action("Index", "Home")" class="btn btn-success">
                                <i class="fas fa-home me-1"></i>Về trang chủ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 