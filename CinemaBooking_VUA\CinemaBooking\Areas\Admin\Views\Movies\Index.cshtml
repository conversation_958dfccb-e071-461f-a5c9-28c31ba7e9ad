@model IEnumerable<CinemaBooking.Models.Phim>

@{
    ViewData["Title"] = "Quản lý phim";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-film me-2"></i>
        Quản lý phim
    </h2>
    <div>
        <a asp-action="Create" class="btn btn-admin">
            <i class="fas fa-plus me-2"></i>
            Thêm phim mới
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Danh sách phim
        </h5>
    </div>
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="row">
                @foreach (var phim in Model)
                {
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 shadow-sm">
                            @if (!string.IsNullOrEmpty(phim.UrlPoster))
                            {
                                <img src="@phim.UrlPoster" class="card-img-top" alt="@phim.TenPhim" style="height: 300px; object-fit: cover;">
                            }
                            else
                            {
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 300px;">
                                    <i class="fas fa-film fa-3x text-muted"></i>
                                </div>
                            }
                            
                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title">@phim.TenPhim</h5>
                                <p class="card-text text-muted small">
                                    <i class="fas fa-clock me-1"></i>
                                    @phim.ThoiLuong phút
                                </p>
                                <p class="card-text text-muted small">
                                    <i class="fas fa-tags me-1"></i>
                                    @phim.TheLoai
                                </p>
                                <p class="card-text text-muted small">
                                    <i class="fas fa-calendar me-1"></i>
                                    @phim.NgayPhatHanh?.ToString("dd/MM/yyyy")
                                </p>
                                
                                @if (phim.LichChieus.Any())
                                {
                                    <p class="card-text">
                                        <span class="badge bg-success">
                                            <i class="fas fa-play me-1"></i>
                                            @phim.LichChieus.Count() suất chiếu
                                        </span>
                                    </p>
                                }
                                else
                                {
                                    <p class="card-text">
                                        <span class="badge bg-warning">
                                            <i class="fas fa-pause me-1"></i>
                                            Chưa có lịch chiếu
                                        </span>
                                    </p>
                                }
                                
                                <div class="mt-auto">
                                    <div class="btn-group w-100" role="group">
                                        <a asp-action="Details" asp-route-id="@phim.MaPhim" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@phim.MaPhim" class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger btn-sm" 
                                                onclick="confirmDelete(@phim.MaPhim, '@phim.TenPhim')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="text-center py-4">
                <i class="fas fa-film fa-3x text-muted mb-3"></i>
                <p class="text-muted">Chưa có phim nào</p>
                <a asp-action="Create" class="btn btn-admin">
                    <i class="fas fa-plus me-2"></i>
                    Thêm phim đầu tiên
                </a>
            </div>
        }
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xác nhận xóa phim</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Bạn có chắc chắn muốn xóa phim <strong id="deleteMovieName"></strong>?</p>
                <p class="text-danger"><small>Hành động này sẽ xóa tất cả lịch chiếu và đánh giá liên quan!</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">Xóa</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(movieId, movieName) {
            document.getElementById('deleteMovieName').textContent = movieName;
            document.getElementById('deleteForm').action = '@Url.Action("Delete")/' + movieId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
}
