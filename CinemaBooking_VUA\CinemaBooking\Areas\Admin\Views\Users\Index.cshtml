@{
    ViewData["Title"] = "Quản lý người dùng";
    var users = ViewBag.Users as List<object>;
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-users me-2"></i>
        Quản lý người dùng
    </h2>
    <div>
        <a href="#" class="btn btn-admin" data-bs-toggle="modal" data-bs-target="#createUserModal">
            <i class="fas fa-plus me-2"></i>
            Thêm người dùng
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Danh sách người dùng
        </h5>
    </div>
    <div class="card-body">
        @if (users != null && users.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Tên đăng nhập</th>
                            <th>Email</th>
                            <th>Họ tên</th>
                            <th>Số điện thoại</th>
                            <th>Vai trò</th>
                            <th>Ngày tạo</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (dynamic userItem in users)
                        {
                            var user = userItem.User as ApplicationUser;
                            var roles = userItem.Roles as IList<string>;
                            
                            <tr>
                                <td><strong>@user.UserName</strong></td>
                                <td>@user.Email</td>
                                <td>@user.HoTen</td>
                                <td>@user.SoDienThoai</td>
                                <td>
                                    @if (roles != null && roles.Any())
                                    {
                                        @foreach (var role in roles)
                                        {
                                            if (role == "Admin")
                                            {
                                                <span class="badge bg-danger me-1">@role</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-primary me-1">@role</span>
                                            }
                                        }
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">Chưa có vai trò</span>
                                    }
                                </td>
                                <td>@user.NgayTao?.ToString("dd/MM/yyyy")</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@user.Id" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@user.Id" class="btn btn-sm btn-outline-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @if (user.UserName != "admin")
                                        {
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmDelete('@user.Id', '@user.UserName')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-4">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <p class="text-muted">Chưa có người dùng nào</p>
            </div>
        }
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xác nhận xóa</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Bạn có chắc chắn muốn xóa người dùng <strong id="deleteUserName"></strong>?</p>
                <p class="text-danger"><small>Hành động này không thể hoàn tác!</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">Xóa</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(userId, userName) {
            document.getElementById('deleteUserName').textContent = userName;
            document.getElementById('deleteForm').action = '@Url.Action("Delete")/' + userId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
}
