﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CinemaBooking.Migrations
{
    /// <inheritdoc />
    public partial class AddCustomColumnsToIdentityTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Add custom columns to AspNetUsers table
            migrationBuilder.AddColumn<string>(
                name: "<PERSON><PERSON><PERSON>",
                table: "AspNetUsers",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SoDienThoai",
                table: "AspNetUsers",
                type: "nvarchar(15)",
                maxLength: 15,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "Ngay<PERSON><PERSON>",
                table: "AspNetUsers",
                type: "datetime2",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove custom columns from AspNetUsers table
            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON>",
                table: "Asp<PERSON>Users");

            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "NgayTao",
                table: "AspNetUsers");
        }
    }
}
