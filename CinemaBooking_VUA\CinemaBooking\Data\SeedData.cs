using CinemaBooking.Models;
using Microsoft.EntityFrameworkCore;

namespace CinemaBooking.Data
{
    public static class SeedData
    {
        public static async Task Initialize(ApplicationDbContext context)
        {
            // Đảm bảo database đã được tạo
            await context.Database.EnsureCreatedAsync();

            // Kiểm tra nếu đã có dữ liệu thì không seed nữa
            if (context.VaiTros.Any())
            {
                return; // DB đã có dữ liệu
            }

            // Thêm vai trò
            var vaiTros = new VaiTro[]
            {
                new VaiTro { TenVaiTro = "Admin", MoTa = "Quản trị viên hệ thống" },
                new VaiTro { TenVaiTro = "User", MoTa = "Người dùng thông thường" }
            };

            foreach (var vaiTro in vaiTros)
            {
                context.VaiTros.Add(vaiTro);
            }
            await context.SaveChangesAsync();

            // Thêm người dùng admin
            var adminUser = new NguoiDung
            {
                TenDangNhap = "admin",
                MatKhau = BCrypt.Net.BCrypt.HashPassword("admin123"),
                Email = "<EMAIL>",
                HoTen = "Quản trị viên",
                SoDienThoai = "0123456789",
                NgayTao = DateTime.Now,
                MaVaiTro = 1 // Admin
            };

            context.NguoiDungs.Add(adminUser);
            await context.SaveChangesAsync();

            // Thêm rạp phim
            var rapPhims = new RapPhim[]
            {
                new RapPhim { TenRap = "CineZore Hà Nội", DiaChi = "123 Đường ABC, Quận 1", ThanhPho = "Hà Nội" },
                new RapPhim { TenRap = "CineZore TP.HCM", DiaChi = "456 Đường XYZ, Quận 3", ThanhPho = "TP.HCM" },
                new RapPhim { TenRap = "CineZore Đà Nẵng", DiaChi = "789 Đường DEF, Quận Hải Châu", ThanhPho = "Đà Nẵng" }
            };

            foreach (var rap in rapPhims)
            {
                context.RapPhims.Add(rap);
            }
            await context.SaveChangesAsync();

            // Thêm phòng chiếu
            var phongChieus = new PhongChieu[]
            {
                // Rạp Hà Nội
                new PhongChieu { MaRap = 1, SoPhong = 1, SucChua = 100 },
                new PhongChieu { MaRap = 1, SoPhong = 2, SucChua = 150 },
                new PhongChieu { MaRap = 1, SoPhong = 3, SucChua = 200 },
                
                // Rạp TP.HCM
                new PhongChieu { MaRap = 2, SoPhong = 1, SucChua = 120 },
                new PhongChieu { MaRap = 2, SoPhong = 2, SucChua = 180 },
                
                // Rạp Đà Nẵng
                new PhongChieu { MaRap = 3, SoPhong = 1, SucChua = 80 },
                new PhongChieu { MaRap = 3, SoPhong = 2, SucChua = 100 }
            };

            foreach (var phong in phongChieus)
            {
                context.PhongChieus.Add(phong);
            }
            await context.SaveChangesAsync();

            // Thêm ghế cho từng phòng
            await SeedSeats(context);

            // Thêm phim
            var phims = new Phim[]
            {
                new Phim 
                { 
                    TenPhim = "Avengers: Endgame", 
                    MoTa = "Cuộc chiến cuối cùng của các siêu anh hùng", 
                    ThoiLuong = 181, 
                    TheLoai = "Hành động, Khoa học viễn tưởng", 
                    NgayPhatHanh = new DateTime(2019, 4, 26),
                    UrlPoster = "/images/avengers-endgame.jpg",
                    DinhDang = "2D, 3D, IMAX",
                    Trailer = "https://www.youtube.com/watch?v=TcMBFSGVi1c"
                },
                new Phim 
                { 
                    TenPhim = "Spider-Man: No Way Home", 
                    MoTa = "Peter Parker phải đối mặt với những kẻ thù từ các vũ trụ khác", 
                    ThoiLuong = 148, 
                    TheLoai = "Hành động, Phiêu lưu", 
                    NgayPhatHanh = new DateTime(2021, 12, 17),
                    UrlPoster = "/images/spiderman-no-way-home.jpg",
                    DinhDang = "2D, 3D, IMAX",
                    Trailer = "https://www.youtube.com/watch?v=JfVOs4VSpmA"
                },
                new Phim 
                { 
                    TenPhim = "Top Gun: Maverick", 
                    MoTa = "Maverick trở lại với nhiệm vụ mới", 
                    ThoiLuong = 130, 
                    TheLoai = "Hành động, Drama", 
                    NgayPhatHanh = new DateTime(2022, 5, 27),
                    UrlPoster = "/images/top-gun-maverick.jpg",
                    DinhDang = "2D, IMAX",
                    Trailer = "https://www.youtube.com/watch?v=qSqVVswa420"
                }
            };

            foreach (var phim in phims)
            {
                context.Phims.Add(phim);
            }
            await context.SaveChangesAsync();

            // Thêm ngôn ngữ phim
            var ngonNguPhims = new NgonNguPhim[]
            {
                new NgonNguPhim { MaPhim = 1, NgonNgu = "Tiếng Anh", PhuDe = "Tiếng Việt" },
                new NgonNguPhim { MaPhim = 1, NgonNgu = "Tiếng Việt", PhuDe = "Không" },
                new NgonNguPhim { MaPhim = 2, NgonNgu = "Tiếng Anh", PhuDe = "Tiếng Việt" },
                new NgonNguPhim { MaPhim = 3, NgonNgu = "Tiếng Anh", PhuDe = "Tiếng Việt" }
            };

            foreach (var ngonNgu in ngonNguPhims)
            {
                context.NgonNguPhims.Add(ngonNgu);
            }
            await context.SaveChangesAsync();

            // Thêm lịch chiếu
            await SeedShowtimes(context);

            // Thêm khuyến mãi
            var khuyenMais = new KhuyenMai[]
            {
                new KhuyenMai 
                { 
                    MaCode = "WELCOME10", 
                    PhanTramGiam = 10, 
                    NgayBatDau = DateTime.Now.AddDays(-30), 
                    NgayKetThuc = DateTime.Now.AddDays(30),
                    MoTa = "Giảm 10% cho khách hàng mới"
                },
                new KhuyenMai 
                { 
                    MaCode = "SUMMER20", 
                    PhanTramGiam = 20, 
                    NgayBatDau = DateTime.Now.AddDays(-15), 
                    NgayKetThuc = DateTime.Now.AddDays(45),
                    MoTa = "Giảm 20% mùa hè"
                }
            };

            foreach (var khuyenMai in khuyenMais)
            {
                context.KhuyenMais.Add(khuyenMai);
            }
            await context.SaveChangesAsync();
        }

        private static async Task SeedSeats(ApplicationDbContext context)
        {
            var phongChieus = await context.PhongChieus.ToListAsync();
            
            foreach (var phong in phongChieus)
            {
                // Tạo ghế theo mẫu: A1, A2, ..., A10, B1, B2, ..., B10, ...
                int soHang = phong.SucChua / 10; // Giả sử mỗi hàng 10 ghế
                
                for (int hang = 0; hang < soHang; hang++)
                {
                    char tenHang = (char)('A' + hang);
                    
                    for (int ghe = 1; ghe <= 10; ghe++)
                    {
                        var gheNew = new Ghe
                        {
                            MaPhong = phong.MaPhong,
                            SoGhe = $"{tenHang}{ghe}",
                            LoaiGhe = (ghe >= 4 && ghe <= 7) ? "VIP" : "Thường"
                        };
                        
                        context.Ghes.Add(gheNew);
                    }
                }
            }
            
            await context.SaveChangesAsync();
        }

        private static async Task SeedShowtimes(ApplicationDbContext context)
        {
            var phims = await context.Phims.ToListAsync();
            var phongChieus = await context.PhongChieus.ToListAsync();
            var ngonNguPhims = await context.NgonNguPhims.ToListAsync();

            var lichChieus = new List<LichChieu>();

            foreach (var phim in phims)
            {
                foreach (var phong in phongChieus.Take(3)) // Chỉ lấy 3 phòng đầu
                {
                    // Tạo lịch chiếu cho 7 ngày tới
                    for (int day = 0; day < 7; day++)
                    {
                        var ngayChieu = DateTime.Today.AddDays(day);
                        
                        // Tạo 3 suất chiếu mỗi ngày
                        var gioChieus = new TimeSpan[] 
                        { 
                            new TimeSpan(9, 0, 0),   // 9:00
                            new TimeSpan(14, 30, 0), // 14:30
                            new TimeSpan(20, 0, 0)   // 20:00
                        };

                        foreach (var gioChieu in gioChieus)
                        {
                            var ngonNgu = ngonNguPhims.FirstOrDefault(n => n.MaPhim == phim.MaPhim);
                            
                            var lichChieu = new LichChieu
                            {
                                MaPhim = phim.MaPhim,
                                MaPhong = phong.MaPhong,
                                NgayChieu = ngayChieu,
                                GioChieu = gioChieu,
                                GiaVe = 100000, // 100,000 VND
                                MaNgonNgu = ngonNgu?.MaNgonNgu
                            };
                            
                            lichChieus.Add(lichChieu);
                        }
                    }
                }
            }

            foreach (var lichChieu in lichChieus)
            {
                context.LichChieus.Add(lichChieu);
            }
            
            await context.SaveChangesAsync();
        }
    }
}
