﻿// <auto-generated />
using System;
using CinemaBooking.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace CinemaBooking.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("CinemaBooking.Models.ApplicationRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("HoTen")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTime?>("NgayTao")
                        .HasColumnType("datetime2");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SoDienThoai")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.DanhGia", b =>
                {
                    b.Property<int>("MaDanhGia")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_danh_gia");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaDanhGia"));

                    b.Property<string>("BinhLuan")
                        .IsRequired()
                        .HasColumnType("ntext")
                        .HasColumnName("binh_luan");

                    b.Property<int?>("DiemSo")
                        .HasColumnType("int")
                        .HasColumnName("diem_so");

                    b.Property<int>("MaNguoiDung")
                        .HasColumnType("int")
                        .HasColumnName("ma_nguoi_dung");

                    b.Property<int>("MaPhim")
                        .HasColumnType("int")
                        .HasColumnName("ma_phim");

                    b.Property<DateTime?>("NgayDanhGia")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_danh_gia")
                        .HasDefaultValueSql("getdate()");

                    b.Property<string>("UserId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("user_id");

                    b.HasKey("MaDanhGia");

                    b.HasIndex("MaNguoiDung");

                    b.HasIndex("MaPhim");

                    b.HasIndex("UserId");

                    b.ToTable("danh_gia", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.DatVe", b =>
                {
                    b.Property<int>("MaDatVe")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_dat_ve");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaDatVe"));

                    b.Property<string>("GhiChu")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("ghi_chu");

                    b.Property<int?>("MaKhuyenMai")
                        .HasColumnType("int")
                        .HasColumnName("ma_khuyen_mai");

                    b.Property<int>("MaLichChieu")
                        .HasColumnType("int")
                        .HasColumnName("ma_lich_chieu");

                    b.Property<int>("MaNguoiDung")
                        .HasColumnType("int")
                        .HasColumnName("ma_nguoi_dung");

                    b.Property<DateTime?>("NgayDat")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_dat")
                        .HasDefaultValueSql("getdate()");

                    b.Property<decimal>("TongTien")
                        .HasColumnType("decimal(10,2)")
                        .HasColumnName("tong_tien");

                    b.Property<string>("TrangThai")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("trang_thai");

                    b.Property<string>("UserId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("user_id");

                    b.HasKey("MaDatVe");

                    b.HasIndex("MaKhuyenMai");

                    b.HasIndex("MaLichChieu");

                    b.HasIndex("MaNguoiDung");

                    b.HasIndex("UserId");

                    b.ToTable("dat_ve", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.DatVeGhe", b =>
                {
                    b.Property<int>("MaDatVe")
                        .HasColumnType("int")
                        .HasColumnName("ma_dat_ve");

                    b.Property<int>("MaGhe")
                        .HasColumnType("int")
                        .HasColumnName("ma_ghe");

                    b.HasKey("MaDatVe", "MaGhe");

                    b.HasIndex("MaGhe");

                    b.ToTable("dat_ve_ghe", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.Ghe", b =>
                {
                    b.Property<int>("MaGhe")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_ghe");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaGhe"));

                    b.Property<string>("LoaiGhe")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasDefaultValue("Thường")
                        .HasColumnName("loai_ghe");

                    b.Property<int>("MaPhong")
                        .HasColumnType("int")
                        .HasColumnName("ma_phong");

                    b.Property<string>("SoGhe")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("so_ghe");

                    b.HasKey("MaGhe");

                    b.HasIndex("MaPhong");

                    b.ToTable("ghe", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.KhuyenMai", b =>
                {
                    b.Property<int>("MaKhuyenMai")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_khuyen_mai");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaKhuyenMai"));

                    b.Property<string>("MaCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("ma_code");

                    b.Property<string>("MoTa")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("mo_ta");

                    b.Property<DateTime>("NgayBatDau")
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_bat_dau");

                    b.Property<DateTime>("NgayKetThuc")
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_ket_thuc");

                    b.Property<int>("PhanTramGiam")
                        .HasColumnType("int")
                        .HasColumnName("phan_tram_giam");

                    b.HasKey("MaKhuyenMai");

                    b.ToTable("khuyen_mai", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.LichChieu", b =>
                {
                    b.Property<int>("MaLichChieu")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_lich_chieu");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaLichChieu"));

                    b.Property<decimal>("GiaVe")
                        .HasColumnType("decimal(10,2)")
                        .HasColumnName("gia_ve");

                    b.Property<TimeSpan>("GioChieu")
                        .HasColumnType("time")
                        .HasColumnName("gio_chieu");

                    b.Property<int?>("MaNgonNgu")
                        .HasColumnType("int")
                        .HasColumnName("ma_ngon_ngu");

                    b.Property<int>("MaPhim")
                        .HasColumnType("int")
                        .HasColumnName("ma_phim");

                    b.Property<int>("MaPhong")
                        .HasColumnType("int")
                        .HasColumnName("ma_phong");

                    b.Property<DateTime>("NgayChieu")
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_chieu");

                    b.HasKey("MaLichChieu");

                    b.HasIndex("MaNgonNgu");

                    b.HasIndex("MaPhim");

                    b.HasIndex("MaPhong");

                    b.ToTable("lich_chieu", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.LichSuGiaoDich", b =>
                {
                    b.Property<int>("MaGiaoDich")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_giao_dich");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaGiaoDich"));

                    b.Property<string>("LoaiGiaoDich")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("loai_giao_dich");

                    b.Property<int?>("MaNguoiDung")
                        .HasColumnType("int")
                        .HasColumnName("ma_nguoi_dung");

                    b.Property<int?>("MaThanhToan")
                        .HasColumnType("int")
                        .HasColumnName("ma_thanh_toan");

                    b.Property<DateTime?>("NgayGiaoDich")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_giao_dich")
                        .HasDefaultValueSql("getdate()");

                    b.Property<string>("NoiDung")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("noi_dung");

                    b.Property<string>("TrangThai")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("trang_thai");

                    b.Property<string>("UserId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("user_id");

                    b.HasKey("MaGiaoDich");

                    b.HasIndex("MaNguoiDung");

                    b.HasIndex("MaThanhToan");

                    b.HasIndex("UserId");

                    b.ToTable("lich_su_giao_dich", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.NgonNguPhim", b =>
                {
                    b.Property<int>("MaNgonNgu")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_ngon_ngu");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaNgonNgu"));

                    b.Property<int?>("MaPhim")
                        .HasColumnType("int")
                        .HasColumnName("ma_phim");

                    b.Property<string>("NgonNgu")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ngon_ngu");

                    b.Property<string>("PhuDe")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("phu_de");

                    b.HasKey("MaNgonNgu");

                    b.HasIndex("MaPhim");

                    b.ToTable("ngon_ngu_phim", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.NguoiDung", b =>
                {
                    b.Property<int>("MaNguoiDung")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_nguoi_dung");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaNguoiDung"));

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("email");

                    b.Property<string>("HoTen")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ho_ten");

                    b.Property<int?>("MaVaiTro")
                        .HasColumnType("int")
                        .HasColumnName("ma_vai_tro");

                    b.Property<string>("MatKhau")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("mat_khau");

                    b.Property<DateTime?>("NgayTao")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_tao")
                        .HasDefaultValueSql("getdate()");

                    b.Property<string>("SoDienThoai")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)")
                        .HasColumnName("so_dien_thoai");

                    b.Property<string>("TenDangNhap")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ten_dang_nhap");

                    b.HasKey("MaNguoiDung");

                    b.HasIndex("MaVaiTro");

                    b.ToTable("nguoi_dung", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.OtpInfo", b =>
                {
                    b.Property<int>("MaOtp")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_otp");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaOtp"));

                    b.Property<bool>("DaSuDung")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("da_su_dung");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("email");

                    b.Property<string>("LoaiOtp")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("loai_otp");

                    b.Property<int?>("MaNguoiDung")
                        .HasColumnType("int")
                        .HasColumnName("ma_nguoi_dung");

                    b.Property<string>("MaXacThuc")
                        .IsRequired()
                        .HasMaxLength(6)
                        .HasColumnType("nvarchar(6)")
                        .HasColumnName("ma_xac_thuc");

                    b.Property<DateTime>("ThoiGianHetHan")
                        .HasColumnType("datetime2")
                        .HasColumnName("thoi_gian_het_han");

                    b.Property<DateTime>("ThoiGianTao")
                        .HasColumnType("datetime2")
                        .HasColumnName("thoi_gian_tao");

                    b.Property<string>("UserId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("user_id");

                    b.HasKey("MaOtp");

                    b.HasIndex("MaNguoiDung");

                    b.HasIndex("UserId");

                    b.ToTable("otp_info", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.Phim", b =>
                {
                    b.Property<int>("MaPhim")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_phim");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaPhim"));

                    b.Property<string>("DinhDang")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("dinh_dang");

                    b.Property<string>("MoTa")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("mo_ta");

                    b.Property<DateTime?>("NgayPhatHanh")
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_phat_hanh");

                    b.Property<string>("TenPhim")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ten_phim");

                    b.Property<string>("TheLoai")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("the_loai");

                    b.Property<int>("ThoiLuong")
                        .HasColumnType("int")
                        .HasColumnName("thoi_luong");

                    b.Property<string>("Trailer")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("trailer");

                    b.Property<string>("UrlPoster")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("url_poster");

                    b.HasKey("MaPhim");

                    b.ToTable("phim", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.PhongChieu", b =>
                {
                    b.Property<int>("MaPhong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_phong");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaPhong"));

                    b.Property<int>("MaRap")
                        .HasColumnType("int")
                        .HasColumnName("ma_rap");

                    b.Property<int>("SoPhong")
                        .HasColumnType("int")
                        .HasColumnName("so_phong");

                    b.Property<int>("SucChua")
                        .HasColumnType("int")
                        .HasColumnName("suc_chua");

                    b.HasKey("MaPhong");

                    b.HasIndex("MaRap");

                    b.ToTable("phong_chieu", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.RapPhim", b =>
                {
                    b.Property<int>("MaRap")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_rap");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaRap"));

                    b.Property<string>("DiaChi")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("dia_chi");

                    b.Property<string>("TenRap")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ten_rap");

                    b.Property<string>("ThanhPho")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("thanh_pho");

                    b.HasKey("MaRap");

                    b.ToTable("rap_phim", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.ThanhToan", b =>
                {
                    b.Property<int>("MaThanhToan")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_thanh_toan");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaThanhToan"));

                    b.Property<string>("GhiChu")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ghi_chu");

                    b.Property<int>("MaDatVe")
                        .HasColumnType("int")
                        .HasColumnName("ma_dat_ve");

                    b.Property<string>("MaGiaoDich")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ma_giao_dich");

                    b.Property<string>("MaGiaoDichNganHang")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ma_giao_dich_ngan_hang");

                    b.Property<DateTime?>("NgayThanhToan")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_thanh_toan")
                        .HasDefaultValueSql("getdate()");

                    b.Property<string>("PhuongThucThanhToan")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("phuong_thuc_thanh_toan");

                    b.Property<decimal>("SoTien")
                        .HasColumnType("decimal(10,2)")
                        .HasColumnName("so_tien");

                    b.Property<string>("TrangThai")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("trang_thai");

                    b.HasKey("MaThanhToan");

                    b.HasIndex("MaDatVe");

                    b.ToTable("thanh_toan", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.VaiTro", b =>
                {
                    b.Property<int>("MaVaiTro")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ma_vai_tro");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaVaiTro"));

                    b.Property<string>("MoTa")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("mo_ta");

                    b.Property<string>("TenVaiTro")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ten_vai_tro");

                    b.HasKey("MaVaiTro");

                    b.ToTable("vai_tro", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("CinemaBooking.Models.DanhGia", b =>
                {
                    b.HasOne("CinemaBooking.Models.NguoiDung", "NguoiDung")
                        .WithMany("DanhGias")
                        .HasForeignKey("MaNguoiDung")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CinemaBooking.Models.Phim", "Phim")
                        .WithMany("DanhGias")
                        .HasForeignKey("MaPhim")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CinemaBooking.Models.ApplicationUser", "ApplicationUser")
                        .WithMany("DanhGias")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("ApplicationUser");

                    b.Navigation("NguoiDung");

                    b.Navigation("Phim");
                });

            modelBuilder.Entity("CinemaBooking.Models.DatVe", b =>
                {
                    b.HasOne("CinemaBooking.Models.KhuyenMai", "KhuyenMai")
                        .WithMany("DatVes")
                        .HasForeignKey("MaKhuyenMai")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("CinemaBooking.Models.LichChieu", "LichChieu")
                        .WithMany("DatVes")
                        .HasForeignKey("MaLichChieu")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CinemaBooking.Models.NguoiDung", "NguoiDung")
                        .WithMany("DatVes")
                        .HasForeignKey("MaNguoiDung")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CinemaBooking.Models.ApplicationUser", "ApplicationUser")
                        .WithMany("DatVes")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("ApplicationUser");

                    b.Navigation("KhuyenMai");

                    b.Navigation("LichChieu");

                    b.Navigation("NguoiDung");
                });

            modelBuilder.Entity("CinemaBooking.Models.DatVeGhe", b =>
                {
                    b.HasOne("CinemaBooking.Models.DatVe", "DatVe")
                        .WithMany("DatVeGhes")
                        .HasForeignKey("MaDatVe")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CinemaBooking.Models.Ghe", "Ghe")
                        .WithMany("DatVeGhes")
                        .HasForeignKey("MaGhe")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DatVe");

                    b.Navigation("Ghe");
                });

            modelBuilder.Entity("CinemaBooking.Models.Ghe", b =>
                {
                    b.HasOne("CinemaBooking.Models.PhongChieu", "PhongChieu")
                        .WithMany("Ghes")
                        .HasForeignKey("MaPhong")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PhongChieu");
                });

            modelBuilder.Entity("CinemaBooking.Models.LichChieu", b =>
                {
                    b.HasOne("CinemaBooking.Models.NgonNguPhim", "NgonNguPhim")
                        .WithMany("LichChieus")
                        .HasForeignKey("MaNgonNgu")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("CinemaBooking.Models.Phim", "Phim")
                        .WithMany("LichChieus")
                        .HasForeignKey("MaPhim")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CinemaBooking.Models.PhongChieu", "PhongChieu")
                        .WithMany("LichChieus")
                        .HasForeignKey("MaPhong")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("NgonNguPhim");

                    b.Navigation("Phim");

                    b.Navigation("PhongChieu");
                });

            modelBuilder.Entity("CinemaBooking.Models.LichSuGiaoDich", b =>
                {
                    b.HasOne("CinemaBooking.Models.NguoiDung", "NguoiDung")
                        .WithMany("LichSuGiaoDichs")
                        .HasForeignKey("MaNguoiDung")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("CinemaBooking.Models.ThanhToan", "ThanhToan")
                        .WithMany()
                        .HasForeignKey("MaThanhToan")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("CinemaBooking.Models.ApplicationUser", "ApplicationUser")
                        .WithMany("LichSuGiaoDichs")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("ApplicationUser");

                    b.Navigation("NguoiDung");

                    b.Navigation("ThanhToan");
                });

            modelBuilder.Entity("CinemaBooking.Models.NgonNguPhim", b =>
                {
                    b.HasOne("CinemaBooking.Models.Phim", "Phim")
                        .WithMany("NgonNguPhims")
                        .HasForeignKey("MaPhim")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Phim");
                });

            modelBuilder.Entity("CinemaBooking.Models.NguoiDung", b =>
                {
                    b.HasOne("CinemaBooking.Models.VaiTro", "VaiTro")
                        .WithMany("NguoiDungs")
                        .HasForeignKey("MaVaiTro")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("VaiTro");
                });

            modelBuilder.Entity("CinemaBooking.Models.OtpInfo", b =>
                {
                    b.HasOne("CinemaBooking.Models.NguoiDung", "NguoiDung")
                        .WithMany("OtpInfos")
                        .HasForeignKey("MaNguoiDung")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("CinemaBooking.Models.ApplicationUser", "ApplicationUser")
                        .WithMany("OtpInfos")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("ApplicationUser");

                    b.Navigation("NguoiDung");
                });

            modelBuilder.Entity("CinemaBooking.Models.PhongChieu", b =>
                {
                    b.HasOne("CinemaBooking.Models.RapPhim", "RapPhim")
                        .WithMany("PhongChieus")
                        .HasForeignKey("MaRap")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RapPhim");
                });

            modelBuilder.Entity("CinemaBooking.Models.ThanhToan", b =>
                {
                    b.HasOne("CinemaBooking.Models.DatVe", "DatVe")
                        .WithMany("ThanhToans")
                        .HasForeignKey("MaDatVe")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DatVe");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("CinemaBooking.Models.ApplicationRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("CinemaBooking.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("CinemaBooking.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("CinemaBooking.Models.ApplicationRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CinemaBooking.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("CinemaBooking.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CinemaBooking.Models.ApplicationUser", b =>
                {
                    b.Navigation("DanhGias");

                    b.Navigation("DatVes");

                    b.Navigation("LichSuGiaoDichs");

                    b.Navigation("OtpInfos");
                });

            modelBuilder.Entity("CinemaBooking.Models.DatVe", b =>
                {
                    b.Navigation("DatVeGhes");

                    b.Navigation("ThanhToans");
                });

            modelBuilder.Entity("CinemaBooking.Models.Ghe", b =>
                {
                    b.Navigation("DatVeGhes");
                });

            modelBuilder.Entity("CinemaBooking.Models.KhuyenMai", b =>
                {
                    b.Navigation("DatVes");
                });

            modelBuilder.Entity("CinemaBooking.Models.LichChieu", b =>
                {
                    b.Navigation("DatVes");
                });

            modelBuilder.Entity("CinemaBooking.Models.NgonNguPhim", b =>
                {
                    b.Navigation("LichChieus");
                });

            modelBuilder.Entity("CinemaBooking.Models.NguoiDung", b =>
                {
                    b.Navigation("DanhGias");

                    b.Navigation("DatVes");

                    b.Navigation("LichSuGiaoDichs");

                    b.Navigation("OtpInfos");
                });

            modelBuilder.Entity("CinemaBooking.Models.Phim", b =>
                {
                    b.Navigation("DanhGias");

                    b.Navigation("LichChieus");

                    b.Navigation("NgonNguPhims");
                });

            modelBuilder.Entity("CinemaBooking.Models.PhongChieu", b =>
                {
                    b.Navigation("Ghes");

                    b.Navigation("LichChieus");
                });

            modelBuilder.Entity("CinemaBooking.Models.RapPhim", b =>
                {
                    b.Navigation("PhongChieus");
                });

            modelBuilder.Entity("CinemaBooking.Models.VaiTro", b =>
                {
                    b.Navigation("NguoiDungs");
                });
#pragma warning restore 612, 618
        }
    }
}
