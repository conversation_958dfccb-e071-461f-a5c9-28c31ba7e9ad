@model IEnumerable<CinemaBooking.Models.Phim>

@{
    ViewData["Title"] = "Phim chiếu ngày " + ViewBag.Date?.ToString("dd/MM/yyyy");
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-calendar-day me-2"></i>Phim chiếu ngày @(ViewBag.Date?.ToString("dd/MM/yyyy") ?? DateTime.Now.ToString("dd/MM/yyyy"))
                    </h3>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <form asp-action="MoviesByDate" method="get" class="row g-3">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                    <input type="date" name="date" class="form-control" value="@(ViewBag.Date?.ToString("yyyy-MM-dd") ?? DateTime.Now.ToString("yyyy-MM-dd"))">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>Xem lịch chiếu
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    @if (Model != null && Model.Any())
                    {
                        <h4 class="mb-3">Danh sách phim (@Model.Count())</h4>
                        <div class="row">
                            @foreach (var phim in Model)
                            {
                                <div class="col-md-3 mb-4">
                                    <div class="movie-container">
                                        <div class="movie-card" data-id="@phim.MaPhim">
                                            <div class="img-hover-zoom">
                                                <img src="@(string.IsNullOrEmpty(phim.UrlPoster) ? "/images/no-image.jpg" : phim.UrlPoster)" class="card-img-top" alt="@phim.TenPhim">
                                            </div>
                                            <div class="card-content">
                                                <h5 class="card-title">@phim.TenPhim</h5>
                                                <span class="movie-genre">@phim.TheLoai</span>
                                                <p class="card-text"><small>@phim.ThoiLuong phút</small></p>
                                                <div class="d-flex gap-2">
                                                    <a asp-controller="Phim" asp-action="Detail" asp-route-id="@phim.MaPhim" class="btn btn-sm btn-danger btn-ripple">
                                                        <i class="fas fa-info-circle"></i> Chi tiết
                                                    </a>
                                                    <a asp-controller="DatVe" asp-action="Index" asp-route-maPhim="@phim.MaPhim" class="btn btn-sm btn-success btn-ripple">
                                                        <i class="fas fa-ticket-alt"></i> Đặt vé
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>Không có phim nào chiếu vào ngày này.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Hiệu ứng hover cho movie card
            $('.movie-card').hover(
                function() {
                    $(this).addClass('hover');
                },
                function() {
                    $(this).removeClass('hover');
                }
            );
        });
    </script>
}
