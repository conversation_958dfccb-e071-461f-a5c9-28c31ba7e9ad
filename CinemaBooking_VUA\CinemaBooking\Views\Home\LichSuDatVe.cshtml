@model IEnumerable<CinemaBooking.Models.DatVe>
@using System.Globalization

@{
    ViewData["Title"] = "Lịch sử đặt vé";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <h1 class="mb-4">L<PERSON>ch sử đặt vé</h1>
        </div>
        <div class="col-md-4 text-end">
            <a href="@Url.Action("TicketHelp", "Home")" class="btn btn-outline-info mb-3">
                <i class="fas fa-question-circle"></i> Hướng dẫn in vé
            </a>
        </div>
    </div>

    <div class="alert alert-info mb-4 no-auto-close" role="alert" id="policyAlert">
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <h5><i class="fas fa-info-circle"></i> <PERSON><PERSON>h sách hủy vé</h5>
                <ul>
                    <li>Vé chưa thanh toán: <PERSON><PERSON> thể hủy miễn phí trước 4 giờ so với giờ chiếu</li>
                    <li>V<PERSON> đã thanh toán:
                        <ul>
                            <li><strong>Không được hoàn tiền</strong> đối với mọi trường hợp hủy vé</li>
                            <li>Chỉ có thể hủy vé trước 4 giờ so với giờ chiếu</li>
                            <li>Không thể hủy vé trong vòng 4 giờ trước giờ chiếu</li>
                        </ul>
                    </li>
                </ul>
            </div>
            <button type="button" class="btn-close" id="closePolicyBtn" aria-label="Close"></button>
        </div>
    </div>

    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (!Model.Any())
    {
        <div class="alert alert-info">
            Bạn chưa có vé đặt nào.
        </div>
    }
    else
    {
        foreach (var datVe in Model)
        {
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">@datVe.LichChieu.Phim.TenPhim</h5>
                    <span class="badge @(datVe.TrangThai == "Đã thanh toán" ? "bg-success" : 
                                         datVe.TrangThai == "Chưa thanh toán" ? "bg-warning" : 
                                         datVe.TrangThai.Contains("Đã hủy") ? "bg-danger" : "bg-secondary")">
                        @datVe.TrangThai
                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Rạp:</strong> @datVe.LichChieu.PhongChieu.RapPhim.TenRap</p>
                            <p><strong>Phòng:</strong> @datVe.LichChieu.PhongChieu.SoPhong</p>
                            <p><strong>Ngày chiếu:</strong> @string.Format(CultureInfo.InvariantCulture, "{0:dd/MM/yyyy}", datVe.LichChieu.NgayChieu)</p>
                            <p><strong>Giờ chiếu:</strong> @($"{datVe.LichChieu.GioChieu.Hours:D2}:{datVe.LichChieu.GioChieu.Minutes:D2}")</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Ngày đặt:</strong> @string.Format(CultureInfo.InvariantCulture, "{0:dd/MM/yyyy HH:mm}", datVe.NgayDat)</p>
                            <p><strong>Tổng tiền:</strong> @string.Format(CultureInfo.InvariantCulture, "{0:N0}", datVe.TongTien) đ</p>
                            @if (datVe.TrangThai.Contains("Đã hủy") && !string.IsNullOrEmpty(datVe.GhiChu))
                            {
                                <p><strong>Ghế:</strong> @(datVe.GhiChu.Contains("Ghế đã đặt:") ? datVe.GhiChu.Replace("Ghế đã đặt: ", "") : datVe.GhiChu)</p>
                            }
                            else if (datVe.DatVeGhes != null && datVe.DatVeGhes.Any())
                            {
                                <p><strong>Ghế:</strong> @string.Join(", ", datVe.DatVeGhes.Select(c => c.Ghe.SoGhe))</p>
                            }
                            else
                            {
                                <p><strong>Ghế:</strong> <span class="text-muted">Không có thông tin</span></p>
                            }
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        @if (!datVe.TrangThai.Contains("Đã hủy"))
                        {
                            <div class="btn-group">
                                <a href="@Url.Action("Print", "DatVe", new { maDatVe = datVe.MaDatVe })" class="btn btn-outline-primary">
                                    <i class="fas fa-print"></i> In vé
                                </a>
                                <button type="button" class="btn btn-outline-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                    <span class="visually-hidden">Chọn kiểu in</span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="@Url.Action("Print", "DatVe", new { maDatVe = datVe.MaDatVe })">
                                            <i class="fas fa-ticket-alt"></i> In vé tổng hợp
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="@Url.Action("PrintMultiple", "DatVe", new { maDatVe = datVe.MaDatVe })">
                                            <i class="fas fa-th-list"></i> In vé theo từng ghế
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        }
                        else
                        {
                            <div></div>
                        }
                        
                        <div>
                            @if (datVe.TrangThai == "Chưa thanh toán" || datVe.TrangThai == "Chờ thanh toán")
                            {
                                <a href="@Url.Action("Index", "ThanhToan", new { maDatVe = datVe.MaDatVe })" class="btn btn-primary me-2">
                                    <i class="fas fa-credit-card"></i> Thanh toán
                                </a>
                                <form asp-controller="DatVe" asp-action="HuyVe" method="post" 
                                      class="d-inline cancel-form" 
                                      data-confirm-message="Bạn có chắc chắn muốn hủy vé này?">
                                    @Html.AntiForgeryToken()
                                    <input type="hidden" name="maDatVe" value="@datVe.MaDatVe" />
                                    <button type="submit" class="btn btn-danger btn-cancel">
                                        <i class="fas fa-times"></i> Hủy vé
                                    </button>
                                </form>
                            }
                            else if (datVe.TrangThai == "Đã thanh toán")
                            {
                                <form asp-controller="DatVe" asp-action="HuyVe" method="post" 
                                      class="d-inline cancel-form" 
                                      data-confirm-message="Bạn có chắc chắn muốn hủy vé này? Lưu ý: Vé đã hủy sẽ không được hoàn tiền.">
                                    @Html.AntiForgeryToken()
                                    <input type="hidden" name="maDatVe" value="@datVe.MaDatVe" />
                                    <button type="submit" class="btn btn-danger btn-cancel">
                                        <i class="fas fa-times"></i> Hủy vé
                                    </button>
                                </form>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
    }
</div>

@section Styles {
    <style>
        .card {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .card-header {
            background-color: #f8f9fa;
        }
        .badge {
            font-size: 0.9rem;
            padding: 0.5em 0.8em;
        }
        .btn-cancel {
            font-weight: bold;
            transition: all 0.2s;
        }
        .btn-cancel:hover {
            transform: scale(1.05);
        }
        #closePolicyBtn {
            margin-top: 10px;
        }
        /* Ngăn site.js đóng thông báo chính sách */
        .no-auto-close {
            pointer-events: initial !important;
        }
    </style>
}

@section Scripts {
    <script>
        $(document).ready(function() {
            // Chặn tự động đóng thông báo
            (function() {
                // Ngăn chặn site.js đóng alert policy
                var policyAlert = document.getElementById('policyAlert');
                
                // Ghi đè phương thức tự động đóng alert từ Bootstrap
                if (policyAlert) {
                    // Loại bỏ class alert-dismissible để tránh bị tự động đóng
                    policyAlert.classList.remove('alert-dismissible');
                    
                    // Tạo một MutationObserver để ngăn chặn bất kỳ script nào thêm class alert-dismissible
                    var observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                                if (policyAlert.classList.contains('alert-dismissible')) {
                                    policyAlert.classList.remove('alert-dismissible');
                                }
                            }
                        });
                    });
                    
                    // Cấu hình observer theo dõi thay đổi class
                    observer.observe(policyAlert, { attributes: true });
                    
                    // Ghi đè event listeners có thể đã được đăng ký
                    policyAlert.outerHTML = policyAlert.outerHTML;
                    
                    // Lấy lại tham chiếu sau khi outerHTML đã thay đổi
                    policyAlert = document.getElementById('policyAlert');
                }
                
                // Ngăn site.js đóng tất cả alert
                var originalSetTimeout = window.setTimeout;
                window.setTimeout = function(callback, timeout) {
                    if (timeout === 5000 && callback.toString().includes('alert')) {
                        // Lọc callback đóng alert sau 5 giây
                        var modifiedCallback = function() {
                            var alerts = document.querySelectorAll('.alert');
                            for (var i = 0; i < alerts.length; i++) {
                                if (alerts[i].id === 'policyAlert' || alerts[i].classList.contains('no-auto-close')) {
                                    // Không đóng những alert này
                                    continue;
                                }
                                // Đóng các alert khác bình thường
                                var closeBtn = alerts[i].querySelector('.btn-close');
                                if (closeBtn) closeBtn.click();
                            }
                        };
                        return originalSetTimeout(modifiedCallback, timeout);
                    }
                    return originalSetTimeout(callback, timeout);
                };
            })();
            
            // Xử lý đóng thông báo chính sách CHỈ khi nhấn nút đóng
            document.getElementById('closePolicyBtn').addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                document.getElementById('policyAlert').style.display = 'none';
            });
            
            // Đảm bảo nút hủy vé hoạt động đúng
            $('.cancel-form').on('submit', function(e) {
                return confirm($(this).data('confirm-message') || 'Bạn có chắc chắn muốn hủy vé này?');
            });
            
            // Thêm hiệu ứng để nút hủy vé nổi bật
            $('.btn-cancel').hover(
                function() {
                    $(this).addClass('pulse');
                },
                function() {
                    $(this).removeClass('pulse');
                }
            );
        });
    </script>
} 