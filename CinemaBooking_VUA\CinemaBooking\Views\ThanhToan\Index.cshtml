@model CinemaBooking.Models.DatVe

@{
    ViewData["Title"] = "Thanh toán vé";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Thông tin đặt vé</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            @if (!string.IsNullOrEmpty(Model.LichChieu.Phim.UrlPoster))
                            {
                                <img src="@Url.Content(Model.LichChieu.Phim.UrlPoster)" alt="@Model.LichChieu.Phim.TenPhim" class="img-fluid rounded" />
                            }
                            else
                            {
                                <img src="~/images/no-image.png" alt="No Image" class="img-fluid rounded" />
                            }
                        </div>
                        <div class="col-md-8">
                            <h4>@Model.LichChieu.Phim.TenPhim</h4>
                            <p class="mb-1"><strong>Rạp:</strong> @Model.LichChieu.PhongChieu.RapPhim.TenRap</p>
                            <p class="mb-1"><strong>Phòng:</strong> @Model.LichChieu.PhongChieu.TenPhong</p>
                            <p class="mb-1"><strong>Suất chiếu:</strong> @Model.LichChieu.NgayChieu.ToString("dd/MM/yyyy") @Model.LichChieu.GioChieu.ToString(@"hh\:mm")</p>
                            <p class="mb-1">
                                <strong>Ghế:</strong>
                                @(Model.DatVeGhes != null && Model.DatVeGhes.Any() ? string.Join(", ", Model.DatVeGhes.Select(g => g.Ghe.SoGhe)) : "")
                            </p>
                            <p class="mb-0"><strong>Tổng tiền:</strong> @Model.TongTien.ToString("N0") VNĐ</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">Phương thức thanh toán</h5>
                </div>
                <div class="card-body">
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger" role="alert">
                            @TempData["ErrorMessage"]
                        </div>
                    }

                    <div class="payment-methods">
                        <div class="card mb-3">
                            <div class="card-body">
                                <form asp-action="ThanhToanTaiRap" method="post" id="payAtCinemaForm">
                                    @Html.AntiForgeryToken()
                                    <input type="hidden" name="maDatVe" value="@Model.MaDatVe" />
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">Thanh toán tại rạp</h6>
                                            <p class="text-muted small mb-0">Thanh toán trực tiếp tại quầy vé trước khi xem phim 30 phút</p>
                                        </div>
                                        <button type="submit" class="btn btn-outline-success">
                                            <i class="fas fa-money-bill-wave me-2"></i>Chọn
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-body">
                                <form asp-action="ThanhToanMomo" method="post" id="momoForm">
                                    @Html.AntiForgeryToken()
                                    <input type="hidden" name="maDatVe" value="@Model.MaDatVe" />
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">Thanh toán qua MoMo</h6>
                                            <p class="text-muted small mb-0">Thanh toán online qua ví điện tử MoMo</p>
                                        </div>
                                        <button type="submit" class="btn btn-momo">
                                            <img src="~/images/momo-logo.png" alt="MoMo" height="20" class="me-2" />
                                            Chọn
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <p class="text-muted small">
                            <i class="fas fa-info-circle"></i> Lưu ý: Vé sẽ tự động hủy nếu không thanh toán trong thời gian quy định.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(function() {
            // Tự động chọn phương thức thanh toán khi click vào card
            $(".payment-method-card").click(function() {
                $(this).find("input[type='radio']").prop("checked", true);
            });
        });
    </script>
} 