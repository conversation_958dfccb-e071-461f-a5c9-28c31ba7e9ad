@model CinemaBooking.Models.ViewModels.SearchViewModel

@{
    ViewData["Title"] = "Tìm kiếm phim";
}

@section Styles {
    <style>
        .search-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 80px 0;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }

        .search-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        .search-hero h1 {
            color: white;
            font-size: 3rem;
            font-weight: 700;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
            margin-bottom: 1rem;
        }

        .search-hero p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }

        .search-form-container {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .search-input-group {
            position: relative;
            margin-bottom: 20px;
        }

        .search-input-group label {
            display: block;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 0.95rem;
        }

        .search-input-group .input-group {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .search-input-group .input-group-text {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 20px;
        }

        .search-input-group .form-control {
            border: none;
            padding: 15px 20px;
            font-size: 1rem;
            background: white;
        }

        .search-input-group .form-control:focus {
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.25);
            border-color: transparent;
        }

        .search-btn {
            background: linear-gradient(135deg, #e50914, #b8070f);
            border: none;
            padding: 15px 40px;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
            box-shadow: 0 8px 25px rgba(229, 9, 20, 0.3);
            transition: all 0.3s ease;
            width: 100%;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(229, 9, 20, 0.4);
            background: linear-gradient(135deg, #f50a15, #c8080e);
        }

        .results-section {
            margin-top: 60px;
        }

        .results-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .results-header h2 {
            color: #333;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .results-count {
            color: #666;
            font-size: 1.1rem;
        }

        .enhanced-movie-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            border: none;
            height: 100%;
        }

        .enhanced-movie-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }

        .enhanced-movie-card .card-img-container {
            position: relative;
            overflow: hidden;
            height: 300px;
        }

        .enhanced-movie-card .card-img-top {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .enhanced-movie-card:hover .card-img-top {
            transform: scale(1.05);
        }

        .enhanced-movie-card .card-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .enhanced-movie-card:hover .card-overlay {
            opacity: 1;
        }

        .enhanced-movie-card .quick-actions {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }

        .enhanced-movie-card:hover .quick-actions {
            opacity: 1;
            transform: translateY(0);
        }

        .enhanced-movie-card .card-body {
            padding: 25px;
        }

        .enhanced-movie-card .card-title {
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .enhanced-movie-card .movie-genre {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 10px;
        }

        .enhanced-movie-card .movie-duration {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .enhanced-movie-card .card-actions {
            display: flex;
            gap: 10px;
        }

        .enhanced-movie-card .btn-detail {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 0.9rem;
            flex: 1;
            transition: all 0.3s ease;
        }

        .enhanced-movie-card .btn-detail:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .enhanced-movie-card .btn-book {
            background: linear-gradient(135deg, #e50914, #b8070f);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 0.9rem;
            flex: 1;
            transition: all 0.3s ease;
        }

        .enhanced-movie-card .btn-book:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.3);
        }

        .no-results {
            text-align: center;
            padding: 60px 20px;
        }

        .no-results i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
        }

        .no-results h3 {
            color: #666;
            margin-bottom: 10px;
        }

        .no-results p {
            color: #999;
        }

        @@keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        @@media (max-width: 768px) {
            .search-hero h1 {
                font-size: 2rem;
            }

            .search-form-container {
                padding: 30px 20px;
            }

            .search-btn {
                margin-top: 20px;
            }
        }
    </style>
}

<!-- Hero Section -->
<div class="search-hero">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h1>Tìm kiếm phim yêu thích</h1>
                <p>Khám phá thế giới điện ảnh với hàng ngàn bộ phim đặc sắc</p>

                <!-- Search Form -->
                <div class="search-form-container">
                    <form asp-action="Index" method="get">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="search-input-group">
                                    <label for="SearchTerm">Tên phim hoặc thể loại</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-film"></i>
                                        </span>
                                        <input type="text" asp-for="SearchTerm" class="form-control"
                                               placeholder="Nhập tên phim hoặc thể loại..." id="SearchTerm">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="search-input-group">
                                    <label for="SearchDate">Ngày chiếu</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-calendar-alt"></i>
                                        </span>
                                        <input type="date" asp-for="SearchDate" class="form-control" id="SearchDate">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="search-btn">
                                    <i class="fas fa-search me-2"></i>Tìm kiếm ngay
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Results Section -->
<div class="container">
    <div class="results-section">

        @if (Model.Results != null && Model.Results.Any())
        {
            <div class="results-header">
                <h2>Kết quả tìm kiếm</h2>
                <p class="results-count">Tìm thấy @Model.Results.Count bộ phim phù hợp</p>
            </div>

            <div class="row g-4">
                @foreach (var phim in Model.Results)
                {
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <div class="enhanced-movie-card">
                            <div class="card-img-container">
                                <img src="@(string.IsNullOrEmpty(phim.UrlPoster) ? "/images/no-image.jpg" : phim.UrlPoster)"
                                     class="card-img-top" alt="@phim.TenPhim">
                                <div class="card-overlay"></div>
                                <div class="quick-actions">
                                    <div class="d-flex gap-2">
                                        <a asp-controller="Phim" asp-action="Detail" asp-route-id="@phim.MaPhim"
                                           class="btn btn-detail btn-sm">
                                            <i class="fas fa-info-circle me-1"></i>Chi tiết
                                        </a>
                                        <a asp-controller="DatVe" asp-action="Index" asp-route-maPhim="@phim.MaPhim"
                                           class="btn btn-book btn-sm">
                                            <i class="fas fa-ticket-alt me-1"></i>Đặt vé
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <h5 class="card-title">@phim.TenPhim</h5>
                                <span class="movie-genre">@phim.TheLoai</span>
                                <p class="movie-duration">
                                    <i class="fas fa-clock me-1"></i>@phim.ThoiLuong phút
                                </p>
                                <div class="card-actions">
                                    <a asp-controller="Phim" asp-action="Detail" asp-route-id="@phim.MaPhim"
                                       class="btn btn-detail">
                                        <i class="fas fa-info-circle me-1"></i>Chi tiết
                                    </a>
                                    <a asp-controller="DatVe" asp-action="Index" asp-route-maPhim="@phim.MaPhim"
                                       class="btn btn-book">
                                        <i class="fas fa-ticket-alt me-1"></i>Đặt vé
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
        else if (!string.IsNullOrEmpty(Model.SearchTerm) || Model.SearchDate.HasValue)
        {
            <div class="no-results">
                <i class="fas fa-search"></i>
                <h3>Không tìm thấy kết quả</h3>
                <p>Không có phim nào phù hợp với tiêu chí tìm kiếm của bạn.</p>
                <p class="text-muted">Hãy thử tìm kiếm với từ khóa khác hoặc thay đổi ngày chiếu.</p>
            </div>
        }
        else
        {
            <div class="no-results">
                <i class="fas fa-film"></i>
                <h3>Bắt đầu tìm kiếm</h3>
                <p>Nhập tên phim hoặc chọn ngày chiếu để tìm kiếm.</p>
                <p class="text-muted">Khám phá hàng ngàn bộ phim đặc sắc đang chờ bạn!</p>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Enhanced search functionality
            const searchForm = $('form');
            const searchBtn = $('.search-btn');
            const originalBtnText = searchBtn.html();

            // Form submission with loading state
            searchForm.on('submit', function() {
                searchBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Đang tìm kiếm...');
                searchBtn.prop('disabled', true);
            });

            // Auto-focus on search input
            $('#SearchTerm').focus();

            // Set default date to today
            if (!$('#SearchDate').val()) {
                const today = new Date().toISOString().split('T')[0];
                $('#SearchDate').attr('min', today);
            }

            // Enhanced card hover effects
            $('.enhanced-movie-card').hover(
                function() {
                    $(this).find('.card-img-top').addClass('hovered');
                },
                function() {
                    $(this).find('.card-img-top').removeClass('hovered');
                }
            );

            // Smooth scroll to results
            if ($('.results-section .enhanced-movie-card').length > 0) {
                $('html, body').animate({
                    scrollTop: $('.results-section').offset().top - 100
                }, 800);
            }
        });
    </script>
}
