@model CinemaBooking.Models.LichChieu

@{
    ViewData["Title"] = "Chọn ghế";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">@Model.Phim.TenPhim</h4>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p><strong>Rạp:</strong> @Model.PhongChieu.RapPhim.TenRap</p>
                            <p><strong>Phòng:</strong> @Model.PhongChieu.SoPhong</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Ngày chiếu:</strong> @Model.NgayChieu.ToString("dd/MM/yyyy")</p>
                            <p><strong><PERSON><PERSON><PERSON> chiếu:</strong> @($"{Model.GioChieu.Hours:D2}:{Model.GioChieu.Minutes:D2}")</p>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h5>Chọn ghế</h5>
                        <div class="capacity-info">
                            <small>Sức chứa phòng: <strong>@Model.PhongChieu.SucChua</strong> ghế | Số hàng: <strong>@(ViewBag.HangGhe.Length)</strong> | Số cột: <strong>@ViewBag.SoCot</strong></small>
                        </div>
                        <div class="screen-container mb-4">
                            <div class="screen">SCREEN</div>
                        </div>
                        
                        <div class="seat-map-container">
                            <table class="table-seats">
                                @{
                                    var gheDaDat = ViewBag.GheDaDat as List<string> ?? new List<string>();
                                    var hangGhe = ViewBag.HangGhe as char[] ?? new[] { 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J' };
                                    var soCot = ViewBag.SoCot as int? ?? 12;
                                    
                                    <!-- Thêm hàng hiển thị số cột -->
                                    <tr>
                                        <td></td> <!-- Ô trống ở góc trái trên -->
                                        @for (int i = 1; i <= soCot; i++)
                                        {
                                            <td class="col-label">@i</td>
                                        }
                                    </tr>
                                    
                                    foreach (var row in hangGhe)
                                    {
                                        <tr>
                                            <td class="row-label">@row</td>
                                            @for (int i = 1; i <= soCot; i++)
                                            {
                                                string seatId = $"{row}{i}";
                                                bool isBooked = gheDaDat.Contains(seatId);
                                                string seatType = "seat-regular";
                                                
                                                // Xác định loại ghế theo vị trí
                                                int hangIndex = Array.IndexOf(hangGhe, row);
                                                int tongSoHang = hangGhe.Length;
                                                
                                                if (hangIndex >= tongSoHang * 0.7) 
                                                {
                                                    // VIP: 30% hàng cuối
                                                    seatType = "seat-vip";
                                                }
                                                
                                                // Sweetbox: Hàng cuối, 4 ghế đầu tiên
                                                if (row == hangGhe[hangGhe.Length - 1] && i <= 4)
                                                {
                                                    seatType = "seat-sweetbox";
                                                }
                                                
                                                <td>
                                                    <button class="seat @seatType @(isBooked ? "seat-booked" : "")" 
                                                        data-seat="@seatId" 
                                                        data-type="@seatType" 
                                                        data-price="@(seatType == "seat-vip" ? Model.GiaVe * 1.2m : (seatType == "seat-sweetbox" ? Model.GiaVe * 1.5m : Model.GiaVe))"
                                                        @(isBooked ? "disabled" : "")>
                                                        @i
                                                    </button>
                                                </td>
                                            }
                                        </tr>
                                    }
                                }
                            </table>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="maKhuyenMai" class="form-label">Mã khuyến mãi (nếu có)</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="maKhuyenMai" name="maKhuyenMai">
                            <button class="btn btn-outline-secondary" type="button" id="btnKiemTraKM">Kiểm tra</button>
                        </div>
                        <div id="khuyenMaiMessage" class="mt-2"></div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <p class="mb-0"><strong>Giá vé:</strong> @string.Format("{0:N0} VNĐ", Model.GiaVe)</p>
                            <p class="mb-0"><strong>Tổng tiền:</strong> <span id="totalAmount">0 VNĐ</span></p>
                        </div>
                        <form id="bookingForm" asp-action="LuuDatVe" method="post">
                            <input type="hidden" name="maLichChieu" value="@Model.MaLichChieu">
                            <input type="hidden" name="selectedSeats" id="selectedSeats">
                            <input type="hidden" name="maKhuyenMai" id="khuyenMaiHidden">
                            <button type="submit" class="btn btn-primary" id="bookButton" disabled>
                                <i class="fas fa-ticket-alt me-2"></i>Đặt vé
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">Chú thích</h5>
                </div>
                <div class="card-body">
                    <div class="legend-item mb-2">
                        <div class="seat seat-regular legend-seat"></div>
                        <span>Ghế thường</span>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="seat seat-vip legend-seat"></div>
                        <span>Ghế VIP</span>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="seat seat-sweetbox legend-seat"></div>
                        <span>Sweetbox</span>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="seat seat-selected legend-seat"></div>
                        <span>Ghế đã chọn</span>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="seat seat-booked legend-seat"></div>
                        <span>Ghế đã đặt</span>
                    </div>
                    <div class="legend-item">
                        <div class="seat seat-booked seat-newly-booked legend-seat"></div>
                        <span>Ghế vừa được đặt</span>
                    </div>
                    <hr>
                    <div class="mt-3">
                        <h6>Ghế đã chọn:</h6>
                        <div id="seatSelectionList">
                            <p>Chưa chọn ghế nào</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .screen-container {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .screen {
            display: inline-block;
            background-color: #e0e0e0;
            width: 80%;
            height: 30px;
            text-align: center;
            line-height: 30px;
            border-top-left-radius: 50%;
            border-top-right-radius: 50%;
            font-weight: bold;
            box-shadow: 0 3px 6px rgba(0,0,0,0.16);
        }
        
        .seat-map-container {
            max-height: 500px;
            overflow-y: auto;
            overflow-x: auto;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            display: flex;
            justify-content: center;
        }
        
        .table-seats {
            margin: 0 auto;
            border-collapse: separate;
            border-spacing: 3px;
        }
        
        .table-seats td {
            padding: 1px;
            text-align: center;
        }
        
        .row-label {
            font-weight: bold;
            width: 25px;
            min-width: 25px;
            font-size: 12px;
            vertical-align: middle;
        }
        
        .col-label {
            font-weight: bold;
            height: 20px;
            font-size: 10px;
            color: #666;
            padding-bottom: 5px;
        }
        
        .seat {
            width: 25px;
            height: 25px;
            border: none;
            border-radius: 4px;
            color: #333;
            font-weight: bold;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.2s;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 1px;
        }
        
        .seat-regular {
            background-color: #ffffff;
            color: #333;
            border: 1px solid #cccccc;
        }
        
        .seat-vip {
            background-color: #f0ad4e;
            color: white;
        }
        
        .seat-sweetbox {
            background-color: #ff69b4;
            color: white;
        }
        
        .seat-selected {
            background-color: #428bca;
            color: white;
            transform: scale(1.1);
            box-shadow: 0 0 8px rgba(66, 139, 202, 0.8);
            border: none;
        }
        
        .seat-booked {
            background-color: #d9534f;
            color: white;
            cursor: not-allowed;
            border: none;
        }
        
        .seat-newly-booked {
            animation: pulse 1.5s infinite;
            box-shadow: 0 0 15px rgba(255, 0, 0, 0.8);
            z-index: 10;
            position: relative;
        }
        
        @@keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.8;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        .seat-hidden {
            visibility: hidden;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .legend-seat {
            width: 20px !important;
            height: 20px !important;
            display: inline-block;
            margin-right: 5px;
        }
        
        #seatSelectionList {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .capacity-info {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
            text-align: center;
        }
        
        /* Thêm media query để điều chỉnh kích thước theo màn hình */
        @@media (max-width: 768px) {
            .seat {
                width: 20px;
                height: 20px;
                font-size: 8px;
                margin: 0;
            }
            
            .row-label, .col-label {
                font-size: 8px;
                width: 20px;
                min-width: 20px;
            }
            
            .table-seats {
                border-spacing: 1px;
            }
        }
    </style>
}

@section Scripts {
    <script>
        $(document).ready(function() {
            var selectedSeats = [];
            var promotionValue = 0;
            var promotionCode = "";
            var promotionDiscount = 0;
            
            // Điều chỉnh vị trí cuộn của seat-map-container
            adjustSeatMapPosition();
            
            // Xử lý chọn ghế
            $('.seat:not(.seat-booked)').on('click', function() {
                var seat = $(this).data('seat');
                var price = $(this).data('price');
                var type = $(this).data('type');
                
                if ($(this).hasClass('seat-selected')) {
                    // Bỏ chọn ghế
                    $(this).removeClass('seat-selected');
                    selectedSeats = selectedSeats.filter(s => s.id !== seat);
                } else {
                    // Chọn ghế
                    $(this).addClass('seat-selected');
                    selectedSeats.push({
                        id: seat,
                        price: price,
                        type: type
                    });
                }
                
                updateSeatSelection();
                updateTotalAmount();
            });
            
            // Xử lý kiểm tra mã khuyến mãi
            $('#btnKiemTraKM').on('click', function() {
                var maKM = $('#maKhuyenMai').val().trim();
                if (!maKM) {
                    $('#khuyenMaiMessage').html('<span class="text-danger">Vui lòng nhập mã khuyến mãi</span>');
                    return;
                }
                
                // Gọi API kiểm tra khuyến mãi
                $.ajax({
                    url: '@Url.Action("KiemTraKhuyenMai", "DatVe")',
                    type: 'POST',
                    data: { maKhuyenMai: maKM },
                    success: function(response) {
                        if (response.isValid) {
                            $('#khuyenMaiMessage').html('<span class="text-success">Mã hợp lệ: Giảm ' + response.phanTramGiam + '%</span>');
                            promotionCode = maKM;
                            promotionDiscount = response.phanTramGiam;
                            $('#khuyenMaiHidden').val(maKM);
                            updateTotalAmount();
                        } else {
                            $('#khuyenMaiMessage').html('<span class="text-danger">Mã không hợp lệ hoặc đã hết hạn</span>');
                            promotionCode = "";
                            promotionDiscount = 0;
                            $('#khuyenMaiHidden').val('');
                            updateTotalAmount();
                        }
                    },
                    error: function() {
                        $('#khuyenMaiMessage').html('<span class="text-danger">Lỗi khi kiểm tra mã</span>');
                    }
                });
            });
            
            // Cập nhật danh sách ghế đã chọn
            function updateSeatSelection() {
                var seatList = $('#seatSelectionList');
                
                if (selectedSeats.length === 0) {
                    seatList.html('<p>Chưa chọn ghế nào</p>');
                    $('#bookButton').prop('disabled', true);
                    $('#selectedSeats').val('');
                } else {
                    var html = '<ul class="list-group list-group-flush">';
                    var seatIds = [];
                    
                    selectedSeats.forEach(function(seat) {
                        var seatTypeText = seat.type === 'seat-vip' ? 'VIP' : 
                                          (seat.type === 'seat-sweetbox' ? 'Sweetbox' : 'Thường');
                        html += '<li class="list-group-item py-1 px-2 d-flex justify-content-between align-items-center">' + 
                                '<span>Ghế ' + seat.id + ' <small>(' + seatTypeText + ')</small></span>' +
                                '<span>' + formatCurrency(seat.price) + '</span>' +
                                '</li>';
                        seatIds.push(seat.id);
                    });
                    
                    html += '</ul>';
                    seatList.html(html);
                    $('#bookButton').prop('disabled', false);
                    $('#selectedSeats').val(seatIds.join(','));
                }
            }
            
            // Cập nhật tổng tiền
            function updateTotalAmount() {
                var total = 0;
                
                selectedSeats.forEach(function(seat) {
                    total += parseFloat(seat.price);
                });
                
                // Áp dụng khuyến mãi nếu có
                if (promotionDiscount > 0) {
                    var discount = (total * promotionDiscount) / 100;
                    total = total - discount;
                }
                
                $('#totalAmount').text(formatCurrency(total));
            }
            
            // Định dạng tiền tệ
            function formatCurrency(amount) {
                return new Intl.NumberFormat('vi-VN', { style: 'decimal' }).format(amount) + ' VNĐ';
            }
            
            // Điều chỉnh vị trí cuộn của seat-map-container để hiển thị ở giữa
            function adjustSeatMapPosition() {
                var container = $('.seat-map-container');
                var tableWidth = $('.table-seats').width();
                var containerWidth = container.width();
                
                if (tableWidth < containerWidth) {
                    // Nếu bảng ghế nhỏ hơn container, căn giữa
                    container.scrollLeft(0);
                } else {
                    // Nếu bảng ghế lớn hơn container, cuộn đến giữa
                    container.scrollLeft((tableWidth - containerWidth) / 2);
                }
                
                // Thiết lập sự kiện scroll mượt mà
                container.css('scroll-behavior', 'smooth');
            }
            
            // Ẩn thanh cuộn ngang khi không cần thiết
            $(window).on('load resize', function() {
                adjustSeatMapPosition();
            });
            
            // Chạy lại kiểm tra trạng thái ghế định kỳ
            var seatCheckInterval = setInterval(checkBookedSeats, 30000); // mỗi 30 giây
            
            // Kiểm tra ghế đã đặt từ server
            function checkBookedSeats() {
                $.ajax({
                    url: '@Url.Action("GetBookedSeats", "DatVe")',
                    type: 'GET',
                    data: { maLichChieu: @Model.MaLichChieu },
                    success: function(response) {
                        if (response.success) {
                            var bookedSeats = response.bookedSeats;
                            var currentlySelected = selectedSeats.map(s => s.id);
                            var newlyBooked = [];
                            
                            // Tìm các ghế vừa được đặt
                            bookedSeats.forEach(function(seat) {
                                if (currentlySelected.includes(seat)) {
                                    newlyBooked.push(seat);
                                }
                                
                                var seatElement = $('.seat[data-seat="' + seat + '"]');
                                if (!seatElement.hasClass('seat-booked')) {
                                    seatElement.addClass('seat-booked');
                                    if (!seatElement.hasClass('seat-newly-booked')) {
                                        seatElement.addClass('seat-newly-booked');
                                    }
                                    seatElement.prop('disabled', true);
                                }
                            });
                            
                            // Nếu có ghế vừa được người khác đặt, cập nhật lại danh sách
                            if (newlyBooked.length > 0) {
                                selectedSeats = selectedSeats.filter(function(seat) {
                                    return !newlyBooked.includes(seat.id);
                                });
                                
                                $('.seat-selected').each(function() {
                                    var seatId = $(this).data('seat');
                                    if (newlyBooked.includes(seatId)) {
                                        $(this).removeClass('seat-selected');
                                    }
                                });
                                
                                updateSeatSelection();
                                updateTotalAmount();
                                
                                if (newlyBooked.length === 1) {
                                    alert('Ghế ' + newlyBooked[0] + ' vừa được người khác đặt. Vui lòng chọn ghế khác.');
                                } else {
                                    alert('Các ghế ' + newlyBooked.join(', ') + ' vừa được người khác đặt. Vui lòng chọn ghế khác.');
                                }
                            }
                            
                            setTimeout(function() {
                                $('.seat-newly-booked').removeClass('seat-newly-booked');
                            }, 5000);
                        }
                    }
                });
            }
            
            // Kiểm tra khi submit form
            $('#bookingForm').on('submit', function(e) {
                if (selectedSeats.length === 0) {
                    e.preventDefault();
                    alert('Vui lòng chọn ít nhất một ghế.');
                    return false;
                }
                return true;
            });
        });
    </script>
} 